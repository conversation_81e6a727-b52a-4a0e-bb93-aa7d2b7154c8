<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Domain\Categories\Services\CategoryDomainService;
use App\Domain\Categories\Services\CategoryHierarchyService;
use App\Domain\Categories\Services\CategoryValidationService;
use App\Domain\Categories\Repositories\CategoryRepositoryInterface;

/**
 * CategoryServiceProvider
 * Categories modülü için service provider
 */
class CategoryServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Domain Services'leri kaydet
        $this->app->singleton(CategoryDomainService::class, function ($app) {
            return new CategoryDomainService(
                $app->make(CategoryRepositoryInterface::class)
            );
        });

        $this->app->singleton(CategoryHierarchyService::class, function ($app) {
            return new CategoryHierarchyService(
                $app->make(CategoryRepositoryInterface::class)
            );
        });

        $this->app->singleton(CategoryValidationService::class, function ($app) {
            return new CategoryValidationService();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [
            CategoryDomainService::class,
            CategoryHierarchyService::class,
            CategoryValidationService::class,
        ];
    }
}
