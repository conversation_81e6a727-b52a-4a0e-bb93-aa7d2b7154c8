<?php

namespace App\Domain\Categories\Services;

use App\Domain\Categories\Entities\Category;
use App\Domain\Categories\ValueObjects\CategorySlug;
use App\Domain\Categories\ValueObjects\CategoryPath;
use App\Domain\Shared\Services\BaseDomainService;

/**
 * CategoryValidationService
 * Kategori validasyon business logic'ini yönetir
 */
class CategoryValidationService extends BaseDomainService
{
    /**
     * Kategori oluşturma validasyonu
     */
    public function validateCategoryCreation(array $categoryData): array
    {
        $errors = [];

        // İsim validasyonu
        if (empty($categoryData['name'])) {
            $errors['name'] = 'Kategori adı zorunludur.';
        } elseif (strlen($categoryData['name']) < 2) {
            $errors['name'] = 'Kategori adı en az 2 karakter olmalıdır.';
        } elseif (strlen($categoryData['name']) > 255) {
            $errors['name'] = '<PERSON>gori adı en fazla 255 karakter olabilir.';
        }

        // Slug validasyonu
        if (empty($categoryData['slug'])) {
            $errors['slug'] = 'Kategori slug\'ı zorunludur.';
        } elseif (!$this->isValidSlug($categoryData['slug'])) {
            $errors['slug'] = 'Geçersiz slug formatı. Sadece küçük harf, rakam ve tire kullanın.';
        }

        // Parent ID validasyonu
        if (isset($categoryData['parent_id'])) {
            $parentValidation = $this->validateParentId($categoryData['parent_id']);
            $errors = array_merge($errors, $parentValidation);
        }

        // Position validasyonu
        if (isset($categoryData['position']) && $categoryData['position'] < 0) {
            $errors['position'] = 'Position negatif olamaz.';
        }

        // Açıklama validasyonu
        if (isset($categoryData['description']) && strlen($categoryData['description']) > 1000) {
            $errors['description'] = 'Açıklama en fazla 1000 karakter olabilir.';
        }

        // Resim validasyonu
        if (isset($categoryData['image'])) {
            $imageValidation = $this->validateImage($categoryData['image']);
            $errors = array_merge($errors, $imageValidation);
        }

        // SEO validasyonu
        if (isset($categoryData['seo'])) {
            $seoValidation = $this->validateSEOData($categoryData['seo']);
            $errors = array_merge($errors, $seoValidation);
        }

        return $errors;
    }

    /**
     * Kategori güncelleme validasyonu
     */
    public function validateCategoryUpdate(Category $category, array $updateData): array
    {
        $errors = [];

        // Mevcut kategori verilerini güncelleme verileriyle birleştir
        $mergedData = array_merge([
            'name' => $category->getName(),
            'slug' => $category->getSlug()->getValue(),
            'parent_id' => $category->getParentId(),
            'description' => $category->getDescription(),
            'position' => $category->getPosition(),
        ], $updateData);

        return $this->validateCategoryCreation($mergedData);
    }

    /**
     * Kategori taşıma validasyonu
     */
    public function validateCategoryMove(Category $category, ?int $newParentId): array
    {
        $errors = [];

        // Kendisini kendi alt kategorisine taşımaya çalışıyor mu?
        if ($newParentId && $newParentId === $category->getId()) {
            $errors['parent_id'] = 'Kategori kendisine taşınamaz.';
        }

        // Parent ID validasyonu
        if ($newParentId) {
            $parentValidation = $this->validateParentId($newParentId, $category->getId());
            $errors = array_merge($errors, $parentValidation);
        }

        // Maksimum derinlik kontrolü
        if ($newParentId) {
            $depthValidation = $this->validateMaxDepth($newParentId);
            $errors = array_merge($errors, $depthValidation);
        }

        return $errors;
    }

    /**
     * Kategori silme validasyonu
     */
    public function validateCategoryDeletion(Category $category): array
    {
        $errors = [];

        // Alt kategori kontrolü
        if ($category->hasChildren()) {
            $errors['children'] = 'Alt kategorileri olan kategori silinemez. Önce alt kategorileri silin veya taşıyın.';
        }

        // Ürün kontrolü
        if ($category->getProductCount() > 0) {
            $errors['products'] = 'Ürünleri olan kategori silinemez. Önce ürünleri başka kategoriye taşıyın.';
        }

        return $errors;
    }

    /**
     * Toplu kategori validasyonu
     */
    public function validateBulkCategories(array $categoriesData): array
    {
        $errors = [];

        foreach ($categoriesData as $index => $categoryData) {
            $categoryErrors = $this->validateCategoryCreation($categoryData);
            
            if (!empty($categoryErrors)) {
                $errors["category_{$index}"] = $categoryErrors;
            }
        }

        // Slug benzersizlik kontrolü
        $slugs = array_column($categoriesData, 'slug');
        $duplicateSlugs = array_diff_assoc($slugs, array_unique($slugs));
        
        if (!empty($duplicateSlugs)) {
            $errors['duplicate_slugs'] = 'Aynı slug birden fazla kategori için kullanılamaz.';
        }

        return $errors;
    }

    /**
     * Parent ID validasyonu
     */
    private function validateParentId(int $parentId, ?int $excludeCategoryId = null): array
    {
        $errors = [];

        if ($parentId <= 0) {
            $errors['parent_id'] = 'Geçersiz parent kategori ID\'si.';
            return $errors;
        }

        // Döngüsel referans kontrolü (Infrastructure layer'da implement edilecek)
        // if ($this->wouldCreateCircularReference($parentId, $excludeCategoryId)) {
        //     $errors['parent_id'] = 'Bu işlem döngüsel referans oluşturacak.';
        // }

        return $errors;
    }

    /**
     * Maksimum derinlik validasyonu
     */
    private function validateMaxDepth(int $parentId, int $maxDepth = 10): array
    {
        $errors = [];

        // Parent kategorinin seviyesini kontrol et (Infrastructure layer'da implement edilecek)
        // $parentLevel = $this->getParentLevel($parentId);
        // 
        // if ($parentLevel >= $maxDepth - 1) {
        //     $errors['max_depth'] = "Maksimum kategori derinliği ({$maxDepth}) aşıldı.";
        // }

        return $errors;
    }

    /**
     * Resim validasyonu
     */
    private function validateImage(string $imagePath): array
    {
        $errors = [];

        if (empty($imagePath)) {
            return $errors;
        }

        // Dosya uzantısı kontrolü
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        $extension = strtolower(pathinfo($imagePath, PATHINFO_EXTENSION));
        
        if (!in_array($extension, $allowedExtensions)) {
            $errors['image'] = 'Geçersiz resim formatı. İzin verilen formatlar: ' . implode(', ', $allowedExtensions);
        }

        // Dosya boyutu kontrolü (Infrastructure layer'da implement edilecek)
        // if ($this->getFileSize($imagePath) > 5 * 1024 * 1024) { // 5MB
        //     $errors['image'] = 'Resim dosyası 5MB\'dan büyük olamaz.';
        // }

        return $errors;
    }

    /**
     * SEO verileri validasyonu
     */
    private function validateSEOData(array $seoData): array
    {
        $errors = [];

        // Meta title validasyonu
        if (isset($seoData['meta_title'])) {
            if (strlen($seoData['meta_title']) > 60) {
                $errors['seo.meta_title'] = 'Meta title 60 karakterden uzun olmamalıdır.';
            }
        }

        // Meta description validasyonu
        if (isset($seoData['meta_description'])) {
            if (strlen($seoData['meta_description']) > 160) {
                $errors['seo.meta_description'] = 'Meta description 160 karakterden uzun olmamalıdır.';
            }
        }

        // Meta keywords validasyonu
        if (isset($seoData['meta_keywords'])) {
            $keywords = explode(',', $seoData['meta_keywords']);
            if (count($keywords) > 10) {
                $errors['seo.meta_keywords'] = 'En fazla 10 anahtar kelime kullanabilirsiniz.';
            }
        }

        // OG title validasyonu
        if (isset($seoData['og_title']) && strlen($seoData['og_title']) > 95) {
            $errors['seo.og_title'] = 'OG title 95 karakterden uzun olmamalıdır.';
        }

        // OG description validasyonu
        if (isset($seoData['og_description']) && strlen($seoData['og_description']) > 200) {
            $errors['seo.og_description'] = 'OG description 200 karakterden uzun olmamalıdır.';
        }

        return $errors;
    }

    /**
     * Slug format kontrolü
     */
    private function isValidSlug(string $slug): bool
    {
        return preg_match('/^[a-z0-9]+(?:-[a-z0-9]+)*$/', $slug);
    }

    /**
     * Kategori hiyerarşisi validasyonu
     */
    public function validateCategoryHierarchy(array $categories): array
    {
        $errors = [];
        $categoryIds = array_column($categories, 'id');
        $parentIds = array_column($categories, 'parent_id');

        // Orphan kategoriler kontrolü
        foreach ($parentIds as $index => $parentId) {
            if ($parentId && !in_array($parentId, $categoryIds)) {
                $errors["category_{$index}"] = 'Parent kategori bulunamadı.';
            }
        }

        // Döngüsel referans kontrolü
        $circularReferences = $this->detectCircularReferences($categories);
        if (!empty($circularReferences)) {
            $errors['circular_references'] = 'Döngüsel referanslar tespit edildi: ' . implode(', ', $circularReferences);
        }

        return $errors;
    }

    /**
     * Döngüsel referans tespiti
     */
    private function detectCircularReferences(array $categories): array
    {
        $circularReferences = [];
        $categoryMap = [];

        // Kategori haritası oluştur
        foreach ($categories as $category) {
            $categoryMap[$category['id']] = $category['parent_id'];
        }

        // Her kategori için döngüsel referans kontrol et
        foreach ($categories as $category) {
            $visited = [];
            $currentId = $category['id'];

            while ($currentId && isset($categoryMap[$currentId])) {
                if (in_array($currentId, $visited)) {
                    $circularReferences[] = $currentId;
                    break;
                }

                $visited[] = $currentId;
                $currentId = $categoryMap[$currentId];
            }
        }

        return array_unique($circularReferences);
    }

    /**
     * Kategori adı benzersizlik kontrolü
     */
    public function validateCategoryNameUniqueness(string $name, ?int $parentId = null, ?int $excludeCategoryId = null): array
    {
        $errors = [];

        // Aynı parent altında aynı isimde kategori var mı kontrol et
        // Bu kontrol Infrastructure layer'da implement edilecek
        // if ($this->categoryExistsWithName($name, $parentId, $excludeCategoryId)) {
        //     $errors['name'] = 'Bu isimde bir kategori zaten mevcut.';
        // }

        return $errors;
    }

    /**
     * Kategori position validasyonu
     */
    public function validateCategoryPosition(int $position, ?int $parentId = null): array
    {
        $errors = [];

        if ($position < 0) {
            $errors['position'] = 'Position negatif olamaz.';
        }

        // Aynı parent altında aynı position var mı kontrol et
        // Bu kontrol Infrastructure layer'da implement edilecek
        // if ($this->positionExistsInParent($position, $parentId)) {
        //     $errors['position'] = 'Bu position zaten kullanımda.';
        // }

        return $errors;
    }

    /**
     * Kategori import validasyonu
     */
    public function validateCategoryImport(array $importData): array
    {
        $errors = [];

        // Gerekli alanlar kontrolü
        $requiredFields = ['name', 'slug'];
        
        foreach ($importData as $index => $categoryData) {
            foreach ($requiredFields as $field) {
                if (!isset($categoryData[$field]) || empty($categoryData[$field])) {
                    $errors["row_{$index}"][$field] = "{$field} alanı zorunludur.";
                }
            }
        }

        // Veri formatı kontrolü
        foreach ($importData as $index => $categoryData) {
            $validationErrors = $this->validateCategoryCreation($categoryData);
            
            if (!empty($validationErrors)) {
                $errors["row_{$index}"] = array_merge($errors["row_{$index}"] ?? [], $validationErrors);
            }
        }

        return $errors;
    }
}
