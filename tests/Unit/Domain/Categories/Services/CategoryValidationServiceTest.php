<?php

namespace Tests\Unit\Domain\Categories\Services;

use Tests\TestCase;
use App\Domain\Categories\Services\CategoryValidationService;
use App\Domain\Categories\Entities\Category;
use App\Domain\Categories\ValueObjects\CategorySlug;
use Mockery;

class CategoryValidationServiceTest extends TestCase
{
    private CategoryValidationService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new CategoryValidationService();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function it_validates_category_creation_successfully()
    {
        // Arrange
        $categoryData = [
            'name' => 'Test Kategori',
            'slug' => 'test-kategori',
            'description' => 'Test açıklama',
            'position' => 1
        ];

        // Act
        $errors = $this->service->validateCategoryCreation($categoryData);

        // Assert
        $this->assertEmpty($errors);
    }

    /** @test */
    public function it_validates_required_fields()
    {
        // Arrange
        $categoryData = [];

        // Act
        $errors = $this->service->validateCategoryCreation($categoryData);

        // Assert
        $this->assertArrayHasKey('name', $errors);
        $this->assertArrayHasKey('slug', $errors);
        $this->assertEquals('Kategori adı zorunludur.', $errors['name']);
        $this->assertEquals('Kategori slug\'ı zorunludur.', $errors['slug']);
    }

    /** @test */
    public function it_validates_name_length()
    {
        // Arrange - Too short name
        $categoryData = [
            'name' => 'A',
            'slug' => 'test-kategori'
        ];

        // Act
        $errors = $this->service->validateCategoryCreation($categoryData);

        // Assert
        $this->assertArrayHasKey('name', $errors);
        $this->assertEquals('Kategori adı en az 2 karakter olmalıdır.', $errors['name']);

        // Arrange - Too long name
        $categoryData['name'] = str_repeat('A', 256);

        // Act
        $errors = $this->service->validateCategoryCreation($categoryData);

        // Assert
        $this->assertArrayHasKey('name', $errors);
        $this->assertEquals('Kategori adı en fazla 255 karakter olabilir.', $errors['name']);
    }

    /** @test */
    public function it_validates_slug_format()
    {
        // Arrange - Invalid slug with uppercase
        $categoryData = [
            'name' => 'Test Kategori',
            'slug' => 'Test-Kategori'
        ];

        // Act
        $errors = $this->service->validateCategoryCreation($categoryData);

        // Assert
        $this->assertArrayHasKey('slug', $errors);
        $this->assertEquals('Geçersiz slug formatı. Sadece küçük harf, rakam ve tire kullanın.', $errors['slug']);

        // Arrange - Invalid slug with special characters
        $categoryData['slug'] = 'test_kategori!';

        // Act
        $errors = $this->service->validateCategoryCreation($categoryData);

        // Assert
        $this->assertArrayHasKey('slug', $errors);
    }

    /** @test */
    public function it_validates_position()
    {
        // Arrange
        $categoryData = [
            'name' => 'Test Kategori',
            'slug' => 'test-kategori',
            'position' => -1
        ];

        // Act
        $errors = $this->service->validateCategoryCreation($categoryData);

        // Assert
        $this->assertArrayHasKey('position', $errors);
        $this->assertEquals('Position negatif olamaz.', $errors['position']);
    }

    /** @test */
    public function it_validates_description_length()
    {
        // Arrange
        $categoryData = [
            'name' => 'Test Kategori',
            'slug' => 'test-kategori',
            'description' => str_repeat('A', 1001)
        ];

        // Act
        $errors = $this->service->validateCategoryCreation($categoryData);

        // Assert
        $this->assertArrayHasKey('description', $errors);
        $this->assertEquals('Açıklama en fazla 1000 karakter olabilir.', $errors['description']);
    }

    /** @test */
    public function it_validates_image_format()
    {
        // Arrange
        $categoryData = [
            'name' => 'Test Kategori',
            'slug' => 'test-kategori',
            'image' => 'test.txt'
        ];

        // Act
        $errors = $this->service->validateCategoryCreation($categoryData);

        // Assert
        $this->assertArrayHasKey('image', $errors);
        $this->assertStringContainsString('Geçersiz resim formatı', $errors['image']);
    }

    /** @test */
    public function it_validates_seo_data()
    {
        // Arrange
        $categoryData = [
            'name' => 'Test Kategori',
            'slug' => 'test-kategori',
            'seo' => [
                'meta_title' => str_repeat('A', 61),
                'meta_description' => str_repeat('B', 161),
                'meta_keywords' => implode(',', array_fill(0, 11, 'keyword')),
                'og_title' => str_repeat('C', 96),
                'og_description' => str_repeat('D', 201)
            ]
        ];

        // Act
        $errors = $this->service->validateCategoryCreation($categoryData);

        // Assert
        $this->assertArrayHasKey('seo.meta_title', $errors);
        $this->assertArrayHasKey('seo.meta_description', $errors);
        $this->assertArrayHasKey('seo.meta_keywords', $errors);
        $this->assertArrayHasKey('seo.og_title', $errors);
        $this->assertArrayHasKey('seo.og_description', $errors);
    }

    /** @test */
    public function it_validates_category_move()
    {
        // Arrange
        $category = $this->createTestCategory();
        $newParentId = 1; // Same as category ID

        // Act
        $errors = $this->service->validateCategoryMove($category, $newParentId);

        // Assert
        $this->assertArrayHasKey('parent_id', $errors);
        $this->assertEquals('Kategori kendisine taşınamaz.', $errors['parent_id']);
    }

    /** @test */
    public function it_validates_category_deletion()
    {
        // Arrange - Category with children
        $category = $this->createTestCategory();
        $category->shouldReceive('hasChildren')->andReturn(true);
        $category->shouldReceive('getProductCount')->andReturn(0);

        // Act
        $errors = $this->service->validateCategoryDeletion($category);

        // Assert
        $this->assertArrayHasKey('children', $errors);
        $this->assertEquals('Alt kategorileri olan kategori silinemez. Önce alt kategorileri silin veya taşıyın.', $errors['children']);

        // Arrange - Category with products
        $category = $this->createTestCategory();
        $category->shouldReceive('hasChildren')->andReturn(false);
        $category->shouldReceive('getProductCount')->andReturn(5);

        // Act
        $errors = $this->service->validateCategoryDeletion($category);

        // Assert
        $this->assertArrayHasKey('products', $errors);
        $this->assertEquals('Ürünleri olan kategori silinemez. Önce ürünleri başka kategoriye taşıyın.', $errors['products']);
    }

    /** @test */
    public function it_validates_bulk_categories()
    {
        // Arrange
        $categoriesData = [
            [
                'name' => 'Kategori 1',
                'slug' => 'kategori-1'
            ],
            [
                'name' => '', // Invalid
                'slug' => 'kategori-2'
            ],
            [
                'name' => 'Kategori 3',
                'slug' => 'kategori-1' // Duplicate slug
            ]
        ];

        // Act
        $errors = $this->service->validateBulkCategories($categoriesData);

        // Assert
        $this->assertArrayHasKey('category_1', $errors);
        $this->assertArrayHasKey('duplicate_slugs', $errors);
    }

    /** @test */
    public function it_detects_circular_references()
    {
        // Arrange
        $categories = [
            ['id' => 1, 'parent_id' => 2],
            ['id' => 2, 'parent_id' => 3],
            ['id' => 3, 'parent_id' => 1] // Circular reference
        ];

        // Act
        $errors = $this->service->validateCategoryHierarchy($categories);

        // Assert
        $this->assertArrayHasKey('circular_references', $errors);
        $this->assertStringContainsString('Döngüsel referanslar tespit edildi', $errors['circular_references']);
    }

    /** @test */
    public function it_validates_category_import()
    {
        // Arrange
        $importData = [
            [
                'name' => 'Kategori 1',
                'slug' => 'kategori-1'
            ],
            [
                'name' => '', // Missing required field
                'slug' => ''  // Missing required field
            ]
        ];

        // Act
        $errors = $this->service->validateCategoryImport($importData);

        // Assert
        $this->assertArrayHasKey('row_1', $errors);
        $this->assertArrayHasKey('name', $errors['row_1']);
        $this->assertArrayHasKey('slug', $errors['row_1']);
    }

    private function createTestCategory(): Category
    {
        $category = Mockery::mock(Category::class);
        $category->shouldReceive('getId')->andReturn(1);
        $category->shouldReceive('getName')->andReturn('Test Kategori');
        $category->shouldReceive('getSlug')->andReturn(CategorySlug::fromString('test-kategori'));
        $category->shouldReceive('getDescription')->andReturn('Test açıklama');
        $category->shouldReceive('getParentId')->andReturn(null);
        
        return $category;
    }
}
