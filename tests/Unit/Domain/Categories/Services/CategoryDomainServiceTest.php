<?php

namespace Tests\Unit\Domain\Categories\Services;

use Tests\TestCase;
use App\Domain\Categories\Services\CategoryDomainService;
use App\Domain\Categories\Entities\Category;
use App\Domain\Categories\ValueObjects\CategorySlug;
use App\Domain\Categories\ValueObjects\SEOData;
use App\Domain\Categories\Repositories\CategoryRepositoryInterface;
use Mockery;

class CategoryDomainServiceTest extends TestCase
{
    private CategoryDomainService $service;
    private CategoryRepositoryInterface $mockRepository;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->mockRepository = Mockery::mock(CategoryRepositoryInterface::class);
        $this->service = new CategoryDomainService($this->mockRepository);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function it_can_create_a_category()
    {
        // Arrange
        $name = 'Test Kategori';
        $slug = CategorySlug::fromString('test-kategori');
        $description = 'Test açıklama';

        $this->mockRepository
            ->shouldReceive('findBySlug')
            ->with($slug)
            ->andReturn(null);

        $this->mockRepository
            ->shouldReceive('findByParentId')
            ->with(null)
            ->andReturn([]);

        // Act
        $category = $this->service->createCategory(
            $name,
            $slug,
            null,
            $description
        );

        // Assert
        $this->assertInstanceOf(Category::class, $category);
        $this->assertEquals($name, $category->getName());
        $this->assertTrue($slug->equals($category->getSlug()));
        $this->assertEquals($description, $category->getDescription());
        $this->assertNull($category->getParentId());
        $this->assertTrue($category->getStatus());
    }

    /** @test */
    public function it_can_create_a_subcategory()
    {
        // Arrange
        $name = 'Alt Kategori';
        $slug = CategorySlug::fromString('alt-kategori');
        $parentId = 1;
        $parentCategory = $this->createTestCategory();

        $this->mockRepository
            ->shouldReceive('findBySlug')
            ->with($slug)
            ->andReturn(null);

        $this->mockRepository
            ->shouldReceive('findById')
            ->with($parentId)
            ->andReturn($parentCategory);

        $this->mockRepository
            ->shouldReceive('findByParentId')
            ->with($parentId)
            ->andReturn([]);

        // Act
        $category = $this->service->createCategory(
            $name,
            $slug,
            $parentId
        );

        // Assert
        $this->assertEquals($parentId, $category->getParentId());
    }

    /** @test */
    public function it_throws_exception_when_slug_already_exists()
    {
        // Arrange
        $slug = CategorySlug::fromString('test-kategori');
        $existingCategory = $this->createTestCategory();

        $this->mockRepository
            ->shouldReceive('findBySlug')
            ->with($slug)
            ->andReturn($existingCategory);

        // Assert
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage("Slug 'test-kategori' zaten kullanımda.");

        // Act
        $this->service->createCategory(
            'Test Kategori',
            $slug
        );
    }

    /** @test */
    public function it_can_update_category_basic_info()
    {
        // Arrange
        $category = $this->createTestCategory();
        $updateData = [
            'name' => 'Güncellenmiş Kategori',
            'slug' => 'guncellenmis-kategori',
            'description' => 'Yeni açıklama'
        ];

        $this->mockRepository
            ->shouldReceive('findBySlug')
            ->andReturn(null);

        // Mock the updateBasicInfo method
        $category->shouldReceive('updateBasicInfo')
            ->with('Güncellenmiş Kategori', Mockery::type(CategorySlug::class), 'Yeni açıklama')
            ->once();

        // Mock return values for updated data
        $category->shouldReceive('getName')->andReturn('Güncellenmiş Kategori');
        $category->shouldReceive('getSlug')->andReturn(CategorySlug::fromString('guncellenmis-kategori'));
        $category->shouldReceive('getDescription')->andReturn('Yeni açıklama');

        // Act
        $updatedCategory = $this->service->updateCategory($category, $updateData);

        // Assert
        $this->assertEquals('Güncellenmiş Kategori', $updatedCategory->getName());
        $this->assertEquals('guncellenmis-kategori', $updatedCategory->getSlug()->getValue());
        $this->assertEquals('Yeni açıklama', $updatedCategory->getDescription());
    }

    /** @test */
    public function it_can_move_category()
    {
        // Arrange
        $category = $this->createTestCategory();
        $newParentId = 2;
        $newParentCategory = $this->createTestCategory();

        $this->mockRepository
            ->shouldReceive('findById')
            ->with($newParentId)
            ->andReturn($newParentCategory);

        $this->mockRepository
            ->shouldReceive('findDescendants')
            ->with($category->getId())
            ->andReturn([]);

        $this->mockRepository
            ->shouldReceive('findByParentId')
            ->with($newParentId)
            ->andReturn([]);

        // Mock the moveToParent and updatePosition methods
        $category->shouldReceive('moveToParent')
            ->with($newParentId)
            ->once();

        $category->shouldReceive('updatePosition')
            ->with(1)
            ->once();

        // Mock return value for getParentId after move
        $category->shouldReceive('getParentId')->andReturn($newParentId);

        // Act
        $movedCategory = $this->service->moveCategory($category, $newParentId);

        // Assert
        $this->assertEquals($newParentId, $movedCategory->getParentId());
    }

    /** @test */
    public function it_throws_exception_when_moving_to_descendant()
    {
        // Arrange
        $category = $this->createTestCategory();
        $descendantId = 3;
        $descendantCategory = $this->createTestCategory();

        $this->mockRepository
            ->shouldReceive('findDescendants')
            ->with($category->getId())
            ->andReturn([$descendantCategory]);

        // Mock descendant category to have the target ID
        $descendantCategory->shouldReceive('getId')->andReturn($descendantId);

        // Mock findById for parent existence check
        $this->mockRepository
            ->shouldReceive('findById')
            ->with($descendantId)
            ->andReturn($descendantCategory);

        // Assert
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Kategori kendi alt kategorisine taşınamaz.');

        // Act
        $this->service->moveCategory($category, $descendantId);
    }

    /** @test */
    public function it_throws_exception_when_deleting_category_with_children()
    {
        // Arrange
        $category = $this->createTestCategory();
        $category->shouldReceive('hasChildren')->andReturn(true);
        $category->shouldReceive('delete')->never(); // Should not be called

        // Assert
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Alt kategorileri olan kategori silinemez. Önce alt kategorileri silin veya taşıyın.');

        // Act
        $this->service->deleteCategory($category);
    }

    /** @test */
    public function it_throws_exception_when_deleting_category_with_products()
    {
        // Arrange
        $category = $this->createTestCategory();
        $category->shouldReceive('hasChildren')->andReturn(false);
        $category->shouldReceive('getProductCount')->andReturn(5);
        $category->shouldReceive('delete')->never(); // Should not be called

        // Assert
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Ürünleri olan kategori silinemez. Önce ürünleri başka kategoriye taşıyın.');

        // Act
        $this->service->deleteCategory($category);
    }

    /** @test */
    public function it_can_update_product_count()
    {
        // Arrange
        $category = $this->createTestCategory();

        // Mock the updateProductCount method
        $category->shouldReceive('updateProductCount')
            ->with(0)
            ->once();

        // Act
        $this->service->updateProductCount($category);

        // Assert - Product count calculation is mocked to return 0
        // In real implementation, this would calculate actual product count
        $this->assertTrue(true); // Test passes if no exception is thrown
    }

    private function createTestCategory(): Category
    {
        $category = Mockery::mock(Category::class);
        $category->shouldReceive('getId')->andReturn(1);
        $category->shouldReceive('getName')->andReturn('Test Kategori');
        $category->shouldReceive('getSlug')->andReturn(CategorySlug::fromString('test-kategori'));
        $category->shouldReceive('getDescription')->andReturn('Test açıklama');
        $category->shouldReceive('getParentId')->andReturn(null);
        $category->shouldReceive('getStatus')->andReturn(true);
        $category->shouldReceive('getLevel')->andReturn(0);
        $category->shouldReceive('getPosition')->andReturn(1);
        $category->shouldReceive('getProductCount')->andReturn(0);
        $category->shouldReceive('hasChildren')->andReturn(false);
        
        return $category;
    }
}
