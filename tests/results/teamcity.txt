##teamcity[testCount count='13' flowId='11700']
##teamcity[testSuiteStarted name='Tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest.php::\Tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest' flowId='11700']
##teamcity[testStarted name='it_validates_bulk_categories' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest.php::\Tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest::it_validates_bulk_categories' flowId='11700']
##teamcity[testFinished name='it_validates_bulk_categories' duration='12' flowId='11700']
##teamcity[testStarted name='it_validates_image_format' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest.php::\Tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest::it_validates_image_format' flowId='11700']
##teamcity[testFinished name='it_validates_image_format' duration='2' flowId='11700']
##teamcity[testStarted name='it_validates_description_length' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest.php::\Tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest::it_validates_description_length' flowId='11700']
##teamcity[testFinished name='it_validates_description_length' duration='2' flowId='11700']
##teamcity[testStarted name='it_validates_slug_format' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest.php::\Tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest::it_validates_slug_format' flowId='11700']
##teamcity[testFinished name='it_validates_slug_format' duration='2' flowId='11700']
##teamcity[testStarted name='it_validates_category_deletion' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest.php::\Tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest::it_validates_category_deletion' flowId='11700']
##teamcity[testFinished name='it_validates_category_deletion' duration='7' flowId='11700']
##teamcity[testStarted name='it_validates_category_creation_successfully' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest.php::\Tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest::it_validates_category_creation_successfully' flowId='11700']
##teamcity[testFinished name='it_validates_category_creation_successfully' duration='2' flowId='11700']
##teamcity[testStarted name='it_validates_seo_data' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest.php::\Tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest::it_validates_seo_data' flowId='11700']
##teamcity[testFinished name='it_validates_seo_data' duration='1' flowId='11700']
##teamcity[testStarted name='it_validates_position' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest.php::\Tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest::it_validates_position' flowId='11700']
##teamcity[testFinished name='it_validates_position' duration='2' flowId='11700']
##teamcity[testStarted name='it_detects_circular_references' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest.php::\Tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest::it_detects_circular_references' flowId='11700']
##teamcity[testFinished name='it_detects_circular_references' duration='2' flowId='11700']
##teamcity[testStarted name='it_validates_category_import' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest.php::\Tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest::it_validates_category_import' flowId='11700']
##teamcity[testFinished name='it_validates_category_import' duration='2' flowId='11700']
##teamcity[testStarted name='it_validates_required_fields' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest.php::\Tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest::it_validates_required_fields' flowId='11700']
##teamcity[testFinished name='it_validates_required_fields' duration='1' flowId='11700']
##teamcity[testStarted name='it_validates_name_length' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest.php::\Tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest::it_validates_name_length' flowId='11700']
##teamcity[testFinished name='it_validates_name_length' duration='2' flowId='11700']
##teamcity[testStarted name='it_validates_category_move' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest.php::\Tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest::it_validates_category_move' flowId='11700']
##teamcity[testFinished name='it_validates_category_move' duration='2' flowId='11700']
##teamcity[testSuiteFinished name='Tests\Unit\Domain\Categories\Services\CategoryValidationServiceTest' flowId='11700']
